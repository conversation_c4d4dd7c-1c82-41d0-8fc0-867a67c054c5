<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      href="resources/favicon.ico"
      type="image/x-icon"
      sizes="96x96"
    />

    <title>Flip the coin | devChallenges.io</title>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .game-container {
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 500px;
        width: 100%;
        margin-bottom: 20px;
      }

      .game-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .game-subtitle {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 40px;
      }

      .coin-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 40px;
      }

      .coin {
        width: 160px;
        height: 160px;
        cursor: pointer;
        transition: transform 0.3s ease;
        position: relative;
        margin-bottom: 20px;
      }

      .coin:hover {
        transform: scale(1.05);
      }

      .coin.flipping {
        animation: flip 1s ease-in-out;
      }

      @keyframes flip {
        0% { transform: rotateY(0deg); }
        50% { transform: rotateY(90deg) scale(1.1); }
        100% { transform: rotateY(180deg); }
      }

      .coin-side {
        position: absolute;
        width: 100%;
        height: 100%;
        backface-visibility: hidden;
        transition: transform 0.6s;
      }

      .coin-heads {
        transform: rotateY(0deg);
      }

      .coin-tails {
        transform: rotateY(180deg);
      }

      .coin.show-tails .coin-heads {
        transform: rotateY(180deg);
      }

      .coin.show-tails .coin-tails {
        transform: rotateY(0deg);
      }

      .coin-shadow {
        width: 100px;
        height: 8px;
        opacity: 0.3;
      }

      .controls {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 30px;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 100px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 2px solid #dee2e6;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .btn:active {
        transform: translateY(0);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .result {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
        min-height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .result.heads {
        color: #28a745;
      }

      .result.tails {
        color: #dc3545;
      }

      .author-info {
        font-size: 14px;
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        margin-top: 20px;
      }

      .author-info a {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
      }

      .author-info a:hover {
        text-decoration: underline;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .game-container {
          padding: 30px 20px;
        }

        .game-title {
          font-size: 2rem;
        }

        .coin {
          width: 140px;
          height: 140px;
        }

        .controls {
          gap: 10px;
        }

        .btn {
          padding: 10px 20px;
          font-size: 0.9rem;
          min-width: 80px;
        }
      }

      @media (max-width: 480px) {
        .game-title {
          font-size: 1.8rem;
        }

        .game-subtitle {
          font-size: 1rem;
        }

        .coin {
          width: 120px;
          height: 120px;
        }

        .controls {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          width: 200px;
        }
      }
    </style>
  </head>
  <body>
    <div class="game-container">
      <h1 class="game-title">Flip a Coin Game</h1>
      <p class="game-subtitle">Press the coin or the button to flip the coin</p>

      <div class="coin-container">
        <div class="coin" id="coin" onclick="flipCoin('random')">
          <div class="coin-side coin-heads">
            <img src="resources/heads.svg" alt="Heads" width="160" height="160">
          </div>
          <div class="coin-side coin-tails">
            <img src="resources/tails.svg" alt="Tails" width="160" height="160">
          </div>
        </div>
        <img src="resources/shadow.svg" alt="Shadow" class="coin-shadow">
      </div>

      <div class="controls">
        <button class="btn btn-secondary" onclick="flipCoin('heads')" id="headsBtn">Head</button>
        <button class="btn btn-secondary" onclick="flipCoin('tails')" id="tailsBtn">Tail</button>
        <button class="btn btn-primary" onclick="flipCoin('random')" id="randomBtn">Random</button>
      </div>

      <div class="result" id="result">Click to flip the coin!</div>
    </div>

    <div class="author-info">
      Coded by <a href="#">Ayokanmi Adejola</a> | Challenge by
      <a href="https://www.devchallenges.io?ref=challenge" target="_blank"
        >devChallenges.io</a
      >.
    </div>

    <script>
      let isFlipping = false;

      function flipCoin(choice) {
        if (isFlipping) return;

        isFlipping = true;
        const coin = document.getElementById('coin');
        const result = document.getElementById('result');
        const buttons = document.querySelectorAll('.btn');

        // Disable buttons during flip
        buttons.forEach(btn => btn.disabled = true);

        // Add flipping animation
        coin.classList.add('flipping');
        result.textContent = 'Flipping...';
        result.className = 'result';

        // Determine result
        let finalResult;
        if (choice === 'random') {
          finalResult = Math.random() < 0.5 ? 'heads' : 'tails';
        } else {
          finalResult = choice;
        }

        // Show result after animation
        setTimeout(() => {
          coin.classList.remove('flipping');

          if (finalResult === 'heads') {
            coin.classList.remove('show-tails');
            result.textContent = 'Heads!';
            result.className = 'result heads';
          } else {
            coin.classList.add('show-tails');
            result.textContent = 'Tails!';
            result.className = 'result tails';
          }

          // Re-enable buttons
          buttons.forEach(btn => btn.disabled = false);
          isFlipping = false;
        }, 1000);
      }

      // Initialize with heads showing
      document.addEventListener('DOMContentLoaded', function() {
        const coin = document.getElementById('coin');
        coin.classList.remove('show-tails');
      });
    </script>
  </body>
</html>
